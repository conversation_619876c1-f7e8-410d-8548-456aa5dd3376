import { BOARD_WIDTH, BOARD_HEIGHT, SCORES, LINES_PER_LEVEL } from './constants.js';

// Create empty game board
export const createBoard = () => {
  return Array(BOARD_HEIGHT).fill(null).map(() => 
    Array(BOARD_WIDTH).fill({ type: null, filled: false })
  );
};

// Check if a position is valid for a piece
export const isValidPosition = (board, piece, x, y) => {
  for (let row = 0; row < piece.shape.length; row++) {
    for (let col = 0; col < piece.shape[row].length; col++) {
      if (piece.shape[row][col]) {
        const newX = x + col;
        const newY = y + row;
        
        // Check boundaries
        if (newX < 0 || newX >= BOARD_WIDTH || newY >= BOARD_HEIGHT) {
          return false;
        }
        
        // Check collision with existing pieces (only if not at top)
        if (newY >= 0 && board[newY][newX].filled) {
          return false;
        }
      }
    }
  }
  return true;
};

// Place a piece on the board
export const placePiece = (board, piece) => {
  const newBoard = board.map(row => [...row]);
  
  for (let row = 0; row < piece.shape.length; row++) {
    for (let col = 0; col < piece.shape[row].length; col++) {
      if (piece.shape[row][col]) {
        const x = piece.x + col;
        const y = piece.y + row;
        
        if (y >= 0 && y < BOARD_HEIGHT && x >= 0 && x < BOARD_WIDTH) {
          newBoard[y][x] = {
            type: piece.type,
            filled: true
          };
        }
      }
    }
  }
  
  return newBoard;
};

// Check for completed lines
export const getCompletedLines = (board) => {
  const completedLines = [];
  
  for (let row = 0; row < BOARD_HEIGHT; row++) {
    if (board[row].every(cell => cell.filled)) {
      completedLines.push(row);
    }
  }
  
  return completedLines;
};

// Clear completed lines and drop remaining blocks
export const clearLines = (board, completedLines) => {
  if (completedLines.length === 0) return board;
  
  let newBoard = board.filter((_, index) => !completedLines.includes(index));
  
  // Add empty rows at the top
  const emptyRows = Array(completedLines.length).fill(null).map(() =>
    Array(BOARD_WIDTH).fill({ type: null, filled: false })
  );
  
  newBoard = [...emptyRows, ...newBoard];
  
  return newBoard;
};

// Calculate score based on lines cleared
export const calculateScore = (linesCleared, level) => {
  const baseScore = (() => {
    switch (linesCleared) {
      case 1: return SCORES.SINGLE;
      case 2: return SCORES.DOUBLE;
      case 3: return SCORES.TRIPLE;
      case 4: return SCORES.TETRIS;
      default: return 0;
    }
  })();
  
  return baseScore * (level + 1);
};

// Calculate level based on lines cleared
export const calculateLevel = (totalLines) => {
  return Math.floor(totalLines / LINES_PER_LEVEL);
};

// Calculate drop speed based on level
export const calculateSpeed = (level, initialSpeed, speedIncrease, minSpeed) => {
  const speed = initialSpeed - (level * speedIncrease);
  return Math.max(speed, minSpeed);
};

// Check if game is over
export const isGameOver = (board) => {
  // Check if any cell in the top row is filled
  return board[0].some(cell => cell.filled);
};

// Get the "ghost" piece position (where piece would land if dropped)
export const getGhostPiece = (board, piece) => {
  let ghostY = piece.y;
  
  while (isValidPosition(board, piece, piece.x, ghostY + 1)) {
    ghostY++;
  }
  
  return { ...piece, y: ghostY };
};

// Hard drop - move piece to bottom immediately
export const hardDrop = (board, piece) => {
  const ghostPiece = getGhostPiece(board, piece);
  const distance = ghostPiece.y - piece.y;
  
  return {
    piece: ghostPiece,
    score: distance * SCORES.HARD_DROP
  };
};
