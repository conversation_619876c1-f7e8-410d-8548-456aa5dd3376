* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

#root {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.app {
  display: flex;
  gap: 30px;
  align-items: flex-start;
  max-width: 1200px;
  width: 100%;
}

.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.game-board {
  display: grid;
  grid-template-columns: repeat(10, 30px);
  grid-template-rows: repeat(20, 30px);
  gap: 1px;
  background: #2c3e50;
  border: 3px solid #34495e;
  border-radius: 10px;
  padding: 5px;
  margin-bottom: 20px;
}

.cell {
  width: 30px;
  height: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  transition: all 0.1s ease;
}

.cell.empty {
  background: #34495e;
}

.cell.filled {
  background: var(--piece-color);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.2);
}

/* Piece colors */
.piece-I { --piece-color: #00f5ff; }
.piece-O { --piece-color: #ffff00; }
.piece-T { --piece-color: #a000ff; }
.piece-S { --piece-color: #00ff00; }
.piece-Z { --piece-color: #ff0000; }
.piece-J { --piece-color: #0000ff; }
.piece-L { --piece-color: #ff8000; }

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 250px;
}

.info-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.info-panel h3 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.score-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 16px;
}

.score-label {
  color: #7f8c8d;
}

.score-value {
  font-weight: bold;
  color: #2c3e50;
}

.next-piece {
  display: grid;
  grid-template-columns: repeat(4, 20px);
  grid-template-rows: repeat(4, 20px);
  gap: 1px;
  margin: 10px auto;
  background: #ecf0f1;
  border-radius: 8px;
  padding: 10px;
}

.next-cell {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  background: #bdc3c7;
}

.next-cell.filled {
  background: var(--piece-color);
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.3);
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.game-over {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.game-over-content {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
}

.game-over h2 {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 32px;
}

.game-over p {
  color: #7f8c8d;
  margin-bottom: 30px;
  font-size: 18px;
}

.instructions {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.instructions h3 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.instructions ul {
  list-style: none;
  padding: 0;
}

.instructions li {
  margin-bottom: 8px;
  color: #7f8c8d;
  font-size: 14px;
}

.instructions kbd {
  background: #ecf0f1;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 12px;
  color: #2c3e50;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .app {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .sidebar {
    width: 100%;
    max-width: 400px;
  }

  .game-board {
    grid-template-columns: repeat(10, 25px);
    grid-template-rows: repeat(20, 25px);
  }

  .cell {
    width: 25px;
    height: 25px;
  }
}

@keyframes lineComplete {
  0% { background: #f39c12; }
  50% { background: #e67e22; }
  100% { background: transparent; }
}

.line-complete {
  animation: lineComplete 0.5s ease-in-out;
}

@keyframes piecePlace {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.piece-placed {
  animation: piecePlace 0.2s ease-in-out;
}

@keyframes gameStart {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

.game-container {
  animation: gameStart 0.5s ease-out;
}

.sidebar {
  animation: gameStart 0.5s ease-out 0.2s both;
}

/* Hover effects for buttons */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

/* Pulse animation for current piece */
.current-piece {
  animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

/* Game over modal animation */
.game-over {
  animation: fadeIn 0.3s ease-out;
}

.game-over-content {
  animation: slideIn 0.5s ease-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideIn {
  0% { transform: translateY(-50px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Responsive touch controls for mobile */
@media (max-width: 768px) {
  .touch-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
  }

  .touch-btn {
    padding: 15px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 50px;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .touch-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.7);
  }
}
