import { useCallback, useRef } from 'react';

// Web Audio API based sound effects
export const useSound = () => {
  const audioContextRef = useRef(null);
  const soundEnabledRef = useRef(true);

  const getAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }
    return audioContextRef.current;
  }, []);

  const playTone = useCallback((frequency, duration, type = 'sine', volume = 0.1) => {
    if (!soundEnabledRef.current) return;

    try {
      const audioContext = getAudioContext();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = type;

      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration);
    } catch (error) {
      console.warn('Sound playback failed:', error);
    }
  }, [getAudioContext]);

  const playMove = useCallback(() => {
    playTone(200, 0.1, 'square', 0.05);
  }, [playTone]);

  const playRotate = useCallback(() => {
    playTone(300, 0.1, 'square', 0.05);
  }, [playTone]);

  const playDrop = useCallback(() => {
    playTone(150, 0.2, 'sawtooth', 0.08);
  }, [playTone]);

  const playLineClear = useCallback((lines) => {
    const frequencies = [400, 500, 600, 700];
    frequencies.slice(0, lines).forEach((freq, index) => {
      setTimeout(() => {
        playTone(freq, 0.3, 'sine', 0.1);
      }, index * 100);
    });
  }, [playTone]);

  const playGameOver = useCallback(() => {
    const notes = [400, 350, 300, 250, 200];
    notes.forEach((freq, index) => {
      setTimeout(() => {
        playTone(freq, 0.5, 'triangle', 0.1);
      }, index * 200);
    });
  }, [playTone]);

  const playLevelUp = useCallback(() => {
    const notes = [300, 400, 500, 600];
    notes.forEach((freq, index) => {
      setTimeout(() => {
        playTone(freq, 0.2, 'sine', 0.08);
      }, index * 100);
    });
  }, [playTone]);

  const toggleSound = useCallback(() => {
    soundEnabledRef.current = !soundEnabledRef.current;
    return soundEnabledRef.current;
  }, []);

  const isSoundEnabled = useCallback(() => {
    return soundEnabledRef.current;
  }, []);

  return {
    playMove,
    playRotate,
    playDrop,
    playLineClear,
    playGameOver,
    playLevelUp,
    toggleSound,
    isSoundEnabled
  };
};
