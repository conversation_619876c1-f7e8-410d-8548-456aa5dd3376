import React from 'react';

const GameOver = ({ score, lines, level, onRestart }) => {
  const formatNumber = (num) => {
    return num.toLocaleString();
  };

  return (
    <div className="game-over">
      <div className="game-over-content">
        <h2>Game Over!</h2>
        <p>Your final score: <strong>{formatNumber(score)}</strong></p>
        <p>Lines cleared: <strong>{formatNumber(lines)}</strong></p>
        <p>Level reached: <strong>{level}</strong></p>
        <div style={{ marginTop: '20px' }}>
          <button 
            className="btn btn-primary"
            onClick={onRestart}
            style={{ marginRight: '10px' }}
          >
            Play Again
          </button>
          <p style={{ marginTop: '15px', fontSize: '14px', color: '#7f8c8d' }}>
            Press <kbd>R</kbd> to restart
          </p>
        </div>
      </div>
    </div>
  );
};

export default GameOver;
