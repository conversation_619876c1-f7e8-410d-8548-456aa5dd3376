import { useState, useEffect, useCallback } from 'react';

const HIGH_SCORE_KEY = 'tetris-high-score';
const HIGH_SCORES_KEY = 'tetris-high-scores';

export const useHighScore = () => {
  const [highScore, setHighScore] = useState(0);
  const [highScores, setHighScores] = useState([]);

  // Load high scores from localStorage on mount
  useEffect(() => {
    try {
      const savedHighScore = localStorage.getItem(HIGH_SCORE_KEY);
      const savedHighScores = localStorage.getItem(HIGH_SCORES_KEY);
      
      if (savedHighScore) {
        setHighScore(parseInt(savedHighScore, 10));
      }
      
      if (savedHighScores) {
        setHighScores(JSON.parse(savedHighScores));
      }
    } catch (error) {
      console.warn('Failed to load high scores:', error);
    }
  }, []);

  // Save high score to localStorage
  const saveHighScore = useCallback((score) => {
    try {
      localStorage.setItem(HIGH_SCORE_KEY, score.toString());
    } catch (error) {
      console.warn('Failed to save high score:', error);
    }
  }, []);

  // Save high scores list to localStorage
  const saveHighScores = useCallback((scores) => {
    try {
      localStorage.setItem(HIGH_SCORES_KEY, JSON.stringify(scores));
    } catch (error) {
      console.warn('Failed to save high scores:', error);
    }
  }, []);

  // Update high score if current score is higher
  const updateHighScore = useCallback((currentScore, lines, level) => {
    let isNewHighScore = false;
    
    if (currentScore > highScore) {
      setHighScore(currentScore);
      saveHighScore(currentScore);
      isNewHighScore = true;
    }

    // Add to high scores list (keep top 10)
    const newEntry = {
      score: currentScore,
      lines,
      level,
      date: new Date().toISOString()
    };

    const updatedScores = [...highScores, newEntry]
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);

    setHighScores(updatedScores);
    saveHighScores(updatedScores);

    return isNewHighScore;
  }, [highScore, highScores, saveHighScore, saveHighScores]);

  // Clear all high scores
  const clearHighScores = useCallback(() => {
    setHighScore(0);
    setHighScores([]);
    try {
      localStorage.removeItem(HIGH_SCORE_KEY);
      localStorage.removeItem(HIGH_SCORES_KEY);
    } catch (error) {
      console.warn('Failed to clear high scores:', error);
    }
  }, []);

  return {
    highScore,
    highScores,
    updateHighScore,
    clearHighScores
  };
};
