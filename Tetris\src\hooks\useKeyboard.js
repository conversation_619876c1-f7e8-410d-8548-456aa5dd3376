import { useEffect, useCallback } from 'react';
import { KEYS } from '../utils/constants.js';

export const useKeyboard = (gameState, onMove, onRotate, onDrop, onHardDrop, onPause, onRestart) => {
  const handleKeyPress = useCallback((event) => {
    if (gameState === 'game_over') {
      if (event.key === KEYS.R || event.key === 'r') {
        onRestart();
      }
      return;
    }

    if (gameState === 'paused') {
      if (event.key === KEYS.P || event.key === 'p' || event.key === KEYS.ESCAPE) {
        onPause();
      }
      return;
    }

    if (gameState !== 'playing') return;

    switch (event.key) {
      case KEYS.LEFT:
        event.preventDefault();
        onMove(-1, 0);
        break;
      case KEYS.RIGHT:
        event.preventDefault();
        onMove(1, 0);
        break;
      case KEYS.DOWN:
        event.preventDefault();
        onDrop();
        break;
      case KEYS.UP:
        event.preventDefault();
        onRotate();
        break;
      case KEYS.SPACE:
        event.preventDefault();
        onHardDrop();
        break;
      case KEYS.P:
      case 'p':
        event.preventDefault();
        onPause();
        break;
      case KEYS.ESCAPE:
        event.preventDefault();
        onPause();
        break;
      case KEYS.R:
      case 'r':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          onRestart();
        }
        break;
      default:
        break;
    }
  }, [gameState, onMove, onRotate, onDrop, onHardDrop, onPause, onRestart]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
};
