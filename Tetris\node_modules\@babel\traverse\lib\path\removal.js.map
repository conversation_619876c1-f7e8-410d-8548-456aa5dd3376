{"version": 3, "names": ["_removal<PERSON>ooks", "require", "_cache", "_replacement", "_index", "_t", "_modification", "_context", "getBindingIdentifiers", "remove", "_this$opts", "_assertUnremoved", "call", "resync", "_callRemovalHooks", "_mark<PERSON><PERSON>oved", "opts", "noScope", "_removeFromScope", "shareCommentsWithSiblings", "_remove", "bindings", "node", "Object", "keys", "for<PERSON>ach", "name", "scope", "removeBinding", "parentPath", "fn", "hooks", "Array", "isArray", "container", "splice", "key", "updateSiblingKeys", "_replaceWith", "_traverseFlags", "SHOULD_SKIP", "REMOVED", "parent", "_getCached<PERSON><PERSON>s", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "removed", "buildCodeFrameError"], "sources": ["../../src/path/removal.ts"], "sourcesContent": ["// This file contains methods responsible for removing a node.\n\nimport { hooks } from \"./lib/removal-hooks.ts\";\nimport { getCachedPaths } from \"../cache.ts\";\nimport { _replaceWith } from \"./replacement.ts\";\nimport type NodePath from \"./index.ts\";\nimport { REMOVED, SHOULD_SKIP } from \"./index.ts\";\nimport { getBindingIdentifiers } from \"@babel/types\";\nimport { updateSiblingKeys } from \"./modification.ts\";\nimport { resync } from \"./context.ts\";\n\nexport function remove(this: NodePath) {\n  _assertUnremoved.call(this);\n\n  resync.call(this);\n\n  if (_callRemovalHooks.call(this)) {\n    _markRemoved.call(this);\n    return;\n  }\n\n  if (!this.opts?.noScope) {\n    _removeFromScope.call(this);\n  }\n\n  this.shareCommentsWithSiblings();\n  _remove.call(this);\n  _markRemoved.call(this);\n}\n\nexport function _removeFromScope(this: NodePath) {\n  const bindings = getBindingIdentifiers(this.node, false, false, true);\n  Object.keys(bindings).forEach(name => this.scope.removeBinding(name));\n}\n\nexport function _callRemovalHooks(this: NodePath) {\n  if (this.parentPath) {\n    for (const fn of hooks) {\n      if (fn(this, this.parentPath)) return true;\n    }\n  }\n}\n\nexport function _remove(this: NodePath) {\n  if (Array.isArray(this.container)) {\n    this.container.splice(this.key as number, 1);\n    updateSiblingKeys.call(this, this.key as number, -1);\n  } else {\n    _replaceWith.call(this, null);\n  }\n}\n\nexport function _markRemoved(this: NodePath) {\n  // this.shouldSkip = true; this.removed = true;\n  this._traverseFlags |= SHOULD_SKIP | REMOVED;\n  if (this.parent) {\n    getCachedPaths(this)?.delete(this.node);\n  }\n  this.node = null;\n}\n\nexport function _assertUnremoved(this: NodePath) {\n  if (this.removed) {\n    throw this.buildCodeFrameError(\n      \"NodePath has been removed so is read-only.\",\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAEA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAAsC;EAF7BO;AAAqB,IAAAH,EAAA;AAIvB,SAASI,MAAMA,CAAA,EAAiB;EAAA,IAAAC,UAAA;EACrCC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3BC,eAAM,CAACD,IAAI,CAAC,IAAI,CAAC;EAEjB,IAAIE,iBAAiB,CAACF,IAAI,CAAC,IAAI,CAAC,EAAE;IAChCG,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;IACvB;EACF;EAEA,IAAI,GAAAF,UAAA,GAAC,IAAI,CAACM,IAAI,aAATN,UAAA,CAAWO,OAAO,GAAE;IACvBC,gBAAgB,CAACN,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEA,IAAI,CAACO,yBAAyB,CAAC,CAAC;EAChCC,OAAO,CAACR,IAAI,CAAC,IAAI,CAAC;EAClBG,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;AACzB;AAEO,SAASM,gBAAgBA,CAAA,EAAiB;EAC/C,MAAMG,QAAQ,GAAGb,qBAAqB,CAAC,IAAI,CAACc,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;EACrEC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI,IAAI,CAACC,KAAK,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC;AACvE;AAEO,SAASZ,iBAAiBA,CAAA,EAAiB;EAChD,IAAI,IAAI,CAACe,UAAU,EAAE;IACnB,KAAK,MAAMC,EAAE,IAAIC,mBAAK,EAAE;MACtB,IAAID,EAAE,CAAC,IAAI,EAAE,IAAI,CAACD,UAAU,CAAC,EAAE,OAAO,IAAI;IAC5C;EACF;AACF;AAEO,SAAST,OAAOA,CAAA,EAAiB;EACtC,IAAIY,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACjC,IAAI,CAACA,SAAS,CAACC,MAAM,CAAC,IAAI,CAACC,GAAG,EAAY,CAAC,CAAC;IAC5CC,+BAAiB,CAACzB,IAAI,CAAC,IAAI,EAAE,IAAI,CAACwB,GAAG,EAAY,CAAC,CAAC,CAAC;EACtD,CAAC,MAAM;IACLE,yBAAY,CAAC1B,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/B;AACF;AAEO,SAASG,YAAYA,CAAA,EAAiB;EAE3C,IAAI,CAACwB,cAAc,IAAIC,kBAAW,GAAGC,cAAO;EAC5C,IAAI,IAAI,CAACC,MAAM,EAAE;IAAA,IAAAC,eAAA;IACf,CAAAA,eAAA,OAAAC,qBAAc,EAAC,IAAI,CAAC,aAApBD,eAAA,CAAsBE,MAAM,CAAC,IAAI,CAACvB,IAAI,CAAC;EACzC;EACA,IAAI,CAACA,IAAI,GAAG,IAAI;AAClB;AAEO,SAASX,gBAAgBA,CAAA,EAAiB;EAC/C,IAAI,IAAI,CAACmC,OAAO,EAAE;IAChB,MAAM,IAAI,CAACC,mBAAmB,CAC5B,4CACF,CAAC;EACH;AACF", "ignoreList": []}