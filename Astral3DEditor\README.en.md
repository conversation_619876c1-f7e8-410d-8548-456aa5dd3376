# <img src="https://editor.astraljs.com/static/images/logo/logo.png" width="30" height="30"> Astral 3D Editor

🌍 [English](README.en.md) | 简体中文

[![Online Demo](https://img.shields.io/badge/Online_Demo-Astral_3D_Editor-8732D7?style=for-the-badge&logo=google-chrome&logoColor=white)](https://editor.astraljs.com)

> Modern Web 3D editor based on Vue3 + Three.js

<div align="center">
  <img src="http://editor-doc.astraljs.com/images/home/<USER>" width="800" alt="Editor Preview">
  <img src="http://editor-doc.astraljs.com/images/home/<USER>" width="800" alt="Editor Preview">
</div>

## 🚀 Core competence

### Core function
-✅ Multi-format support: 30+ model format (GLTF/OBJ/FBX/GLB/RVT/IFC, etc.)
- ✅ BIM Model Lightweight Demonstration (RVT/IFC)
-✅ CAD drawing analysis (DWG/DXF)
- ✅ Scenario subcontract storage and loading
- ✅ WebSocket Multi-party collaboration

### Expansion capability
- 🧩 plug-in system
- 📜 When the script is running
- 💫 Particle system
- ❄️ Weather system
- ☁️ Cloud Storage Integration (USS again)

### Coming soon
- 🚧 Animation editor
- 🚧 Physical engine support
- 🚧 WebGPU support
- 🚧 Data components (API/WebSocket)
- 🚧 low code data large screen

## 🛠️ Technology stack

![Vue3](https://img.shields.io/badge/Vue-3.3.4-4FC08D?logo=vuedotjs)
![Three.js](https://img.shields.io/badge/Three.js-r170-000000?logo=threedotjs)
![Cesium](https://img.shields.io/badge/Cesium-1.107.0-0133B4?logo=cesium)
![NaiveUI](https://img.shields.io/badge/Naive_UI-2.39.0-66C060?logo=vue.js)
![UnoCSS](https://img.shields.io/badge/UnoCSS-0.46.5-333333?logo=unocss)
![Go](https://img.shields.io/badge/Backend-Go_1.20-00ADD8?logo=go)
![MIT License](https://img.shields.io/badge/License-Apache_2.0-blue.svg)

## ⚡ Quick start

### pre-demand
- Node.js ≥ 18.x
- Yarn

### Local run
```bash
git clone https://github.com/mlt131220/Astral3DEditor.git
cd Astral3DEditor
yarn install
yarn dev
```

> 📢 Note: default to test the back-end ` http://43.140.200.138:8080 ` (data not emptying)

### Production construction
```bash
yarn build
```

## 📚 Ecological correlation

### Back-end implementation
[![Go Backend](https://img.shields.io/badge/Back_end_implementation-Astral3DEditorGoBack-00ADD8?logo=go)](https://github.com/mlt131220/ES3DEditorGoBack)

### Document center
[![Documentation](https://img.shields.io/badge/Document_center-Astral_Docs-8732D7?logo=gitbook)](http://editor-doc.astraljs.com/)

## 💬 Join the community

Get the latest news and technical support in the following ways:

[![WeChat Group](https://img.shields.io/badge/WeChat_Group-Scan_code_to_join-07C160?logo=wechat&logoColor=white)](https://upyun.astraljs.com/static/images/WeChatGroup.jpg)
[![Contact Author](https://img.shields.io/badge/Contact_author-Personal_wechat-07C160?logo=wechat&logoColor=white)](https://upyun.astraljs.com/static/images/ContactMe.jpg)

## ☕ Support project

If this project is helpful to you, please feel free to:

1. In case wall [user] (https://github.com/mlt131220/ES-3DEditor/issues/2) leave your usage scenario
2. Scan code support developers:

| AliPay                                                                      | WeChat                                                                     |
|-----------------------------------------------------------------------------|----------------------------------------------------------------------------|
| <img src="https://upyun.astraljs.com/static/images/alipay.jpg" width="200"> | <img src="https://upyun.astraljs.com/static/images/wepay.jpg" width="200"> |

## ⚖️ License agreement

This project uses the **[Apache-2.0 License](LICENSE)** open source license, please comply with the following terms:

- ✅ Allowed: personal learning, secondary development, non-commercial use
- ⚠️ Authorization required: Commercial use
- ❌ Do not use this item for any illegal purpose

**[Full legal notice](LEGAL.md)** | **[Contribution guide](CONTRIBUTING.md)**

## 🌟 Star trend

[![Star History Chart](https://api.star-history.com/svg?repos=mlt131220/Astral3DEditor&type=Date)](https://star-history.com/#mlt131220/Astral3DEditor&Date)