import React from 'react';

const GameInfo = ({ score, lines, level, highScore }) => {
  const formatNumber = (num) => {
    return num.toLocaleString();
  };

  return (
    <div className="info-panel">
      <h3>Game Stats</h3>
      <div className="score-item">
        <span className="score-label">Score:</span>
        <span className="score-value">{formatNumber(score)}</span>
      </div>
      <div className="score-item">
        <span className="score-label">High Score:</span>
        <span className="score-value" style={{ color: score >= highScore && score > 0 ? '#e74c3c' : '#2c3e50' }}>
          {formatNumber(highScore)}
        </span>
      </div>
      <div className="score-item">
        <span className="score-label">Lines:</span>
        <span className="score-value">{formatNumber(lines)}</span>
      </div>
      <div className="score-item">
        <span className="score-label">Level:</span>
        <span className="score-value">{level}</span>
      </div>
    </div>
  );
};

export default GameInfo;
