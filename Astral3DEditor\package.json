{"name": "astral-3d-editor", "author": "ErSan <<EMAIL>>", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "tsc": "tsc", "preview": "vite preview"}, "dependencies": {"@ant-design/colors": "^7.0.2", "@dxfom/mtext": "0.3.2", "@monaco-editor/loader": "^1.4.0", "@vicons/carbon": "^0.12.0", "@vicons/fa": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "animate.css": "^4.1.1", "axios": "1.6.0", "camera-controls": "^2.9.0", "cesium": "1.107.0", "js-base64": "^3.7.5", "jszip": "3.10.1", "keyframe-resample": "^0.1.0", "localforage": "^1.10.0", "loglevel": "1.7.1", "naive-ui": "2.39.0", "pako": "2.1.0", "pinia": "2.1.4", "signals": "1.0.0", "three": "0.163.0", "three-mesh-bvh": "0.7.3", "vite-plugin-pwa": "^0.21.2", "vue": "3.3.4", "vue-hooks-plus": "1.8.9", "vue-i18n": "9.2.2", "vue-router": "^4.3.2", "web-ifc-three": "^0.0.126"}, "devDependencies": {"@types/cesium": "^1.70.0", "@types/fs-extra": "^11.0.3", "@types/node": "^18.11.9", "@types/three": "^0.163.0", "@unocss/preset-attributify": "^0.46.5", "@unocss/preset-icons": "^0.46.5", "@vitejs/plugin-vue": "^3.2.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "less": "4.1.3", "rollup-plugin-visualizer": "^5.9.2", "typescript": "^4.9.3", "unocss": "^0.46.5", "unplugin-config": "^0.1.3", "unplugin-vue-components": "^0.27.3", "vite": "5.0.12", "vite-plugin-cesium": "1.2.22", "vite-plugin-compression": "^0.5.1", "vite-plugin-enhance-log": "^0.6.2", "vite-plugin-top-level-await": "^1.3.1"}}