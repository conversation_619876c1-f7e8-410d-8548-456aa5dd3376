# <img src="https://editor.astraljs.com/static/images/logo/logo.png" width="30" height="30"> Astral 3D Editor

🌍 [English](README.en.md) | 简体中文

[![Online Demo](https://img.shields.io/badge/Online_Demo-Astral_3D_Editor-8732D7?style=for-the-badge&logo=google-chrome&logoColor=white)](https://editor.astraljs.com)

> 基于 Vue3 + Three.js 的现代 Web 3D 编辑器

<div align="center">
  <img src="http://editor-doc.astraljs.com/images/home/<USER>" width="800" alt="Editor City Preview">
  <img src="http://editor-doc.astraljs.com/images/home/<USER>" width="800" alt="Editor Preview">
</div>

## 💬 加入社区

通过以下方式获取最新动态和技术支持：

| [![QQ Group](https://img.shields.io/badge/QQ交流群-1040320579-07C160?logo=wechat&logoColor=white)](https://upyun.astraljs.com/static/images/QQGroup.jpg)                                                                         | [![Contact Author](https://img.shields.io/badge/联系作者-个人微信-07C160?logo=wechat&logoColor=white)](https://upyun.astraljs.com/static/images/ContactMe.jpg)                                                                      |
|-----------------------------------------------------------------------------|----------------------------------------------------------------------------|
| <img src="https://upyun.astraljs.com/static/images/QQGroup.jpg" width="200"> | <img src="https://upyun.astraljs.com/static/images/ContactMe.jpg" width="200"> |




## 🚀 核心能力

### 核心功能
- ✅ 多格式支持：30+ 模型格式（GLTF/OBJ/FBX/GLB/RVT/IFC等）
- ✅ BIM模型轻量化展示（RVT/IFC）
- ✅ CAD图纸解析（DWG/DXF）
- ✅ 场景分包存储与加载
- ✅ WebSocket 多人协作

### 扩展能力
- 🧩 插件系统
- 📜 脚本运行时
- 💫 粒子系统
- ❄️ 天气系统
- ☁️ 云存储集成（又拍云 USS）
- 🎠 动画编辑器

### 即将到来
- 🚧 物理引擎支持
- 🚧 WebGPU 支持
- 🚧 数据组件（API/WebSocket）
- 🚧 低代码数据大屏

## 🛠️ 技术栈

![Vue3](https://img.shields.io/badge/Vue-3.3.4-4FC08D?logo=vuedotjs)
![Three.js](https://img.shields.io/badge/Three.js-r170-000000?logo=threedotjs)
![Cesium](https://img.shields.io/badge/Cesium-1.107.0-0133B4?logo=cesium)
![NaiveUI](https://img.shields.io/badge/Naive_UI-2.39.0-66C060?logo=vue.js)
![UnoCSS](https://img.shields.io/badge/UnoCSS-0.46.5-333333?logo=unocss)
![Go](https://img.shields.io/badge/Backend-Go_1.20-00ADD8?logo=go)
![MIT License](https://img.shields.io/badge/License-Apache_2.0-blue.svg)

## ⚡ 快速开始

### 前置需求
- Node.js ≥ 18.x
- Yarn

### 本地运行
```bash
git clone https://github.com/mlt131220/Astral3DEditor.git
cd Astral3DEditor
yarn install
yarn run dev
```

> 📢 提示：默认使用测试后端 `http://43.140.200.138:8080`（数据不定期清空）

### 生产构建
```bash
yarn run build
```

## 📚 生态相关

### 后端实现
[![Go Backend](https://img.shields.io/badge/后端实现-Astral3DEditorGoBack-00ADD8?logo=go)](https://github.com/mlt131220/ES3DEditorGoBack)

### 文档中心
[![Documentation](https://img.shields.io/badge/文档中心-Astral_Docs-8732D7?logo=gitbook)](http://editor-doc.astraljs.com/)

## ☕ 支持项目

如果本项目对您有帮助，欢迎：

1. 在 [用户案例墙](https://github.com/mlt131220/ES-3DEditor/issues/2) 留下您的使用场景
2. 扫码支持开发者：

| 支付宝                                                                         | 微信支付                                                                       |
|-----------------------------------------------------------------------------|----------------------------------------------------------------------------|
| <img src="https://upyun.astraljs.com/static/images/alipay.jpg" width="200"> | <img src="https://upyun.astraljs.com/static/images/wepay.jpg" width="200"> |

## ⚖️ 许可协议

本项目采用 **[Apache-2.0 License](LICENSE)** 开源协议，使用时请遵守以下条款：

- ✅ 允许：个人学习、二次开发、非商业用途
- ⚠️ 需要授权：商业用途
- ❌ 禁止：将本项目用于非法用途

**[完整法律声明](LEGAL.md)** | **[贡献指南](CONTRIBUTING.md)**

## 🌟 Star 趋势

[![Star History Chart](https://api.star-history.com/svg?repos=mlt131220/Astral3DEditor&type=Date)](https://star-history.com/#mlt131220/Astral3DEditor&Date)
