import React from 'react';

const GameControls = ({ gameState, onPause, onRestart, onToggleSound, isSoundEnabled }) => {
  const getPauseButtonText = () => {
    switch (gameState) {
      case 'playing':
        return 'Pause';
      case 'paused':
        return 'Resume';
      default:
        return 'Pause';
    }
  };

  return (
    <div className="info-panel">
      <h3>Controls</h3>
      <div className="controls">
        <button
          className="btn btn-primary"
          onClick={onPause}
          disabled={gameState === 'game_over'}
        >
          {getPauseButtonText()}
        </button>
        <button
          className="btn btn-danger"
          onClick={onRestart}
        >
          New Game
        </button>
        <button
          className="btn btn-secondary"
          onClick={onToggleSound}
          title={isSoundEnabled ? 'Disable Sound' : 'Enable Sound'}
        >
          {isSoundEnabled ? '🔊' : '🔇'}
        </button>
      </div>
    </div>
  );
};

export default GameControls;
