.grid-txt {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.grid-sidle {
	flex: 5;
	padding-left: 10px;
}
.grid-style {
	min-width: 110px;
}
.element-tag {
	overflow: hidden;
	font-family: Helvetica, sans-serif;
	font-size: 6px;
	line-height: normal;
	text-align: center;
	cursor: pointer;
	box-shadow: 0 0 4px rgb(0 255 255 / 50%);
	
}
.tag-icon {
	font-size: 12px;
	color: #ffffff;
	text-align: center;
	pointer-events: auto;
	cursor: pointer;
}





