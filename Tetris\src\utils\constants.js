// Game board dimensions
export const BOARD_WIDTH = 10;
export const BOARD_HEIGHT = 20;

// Game speeds (milliseconds between drops)
export const INITIAL_SPEED = 1000;
export const SPEED_INCREASE = 50;
export const MIN_SPEED = 100;

// Scoring system
export const SCORES = {
  SINGLE: 100,
  DOUBLE: 300,
  TRIPLE: 500,
  TETRIS: 800,
  SOFT_DROP: 1,
  HARD_DROP: 2
};

// Level progression
export const LINES_PER_LEVEL = 10;

// Key codes
export const KEYS = {
  LEFT: 'ArrowLeft',
  RIGHT: 'ArrowRight',
  DOWN: 'ArrowDown',
  UP: 'ArrowUp',
  SPACE: ' ',
  ESCAPE: 'Escape',
  P: 'p',
  R: 'r'
};

// Game states
export const GAME_STATES = {
  PLAYING: 'playing',
  PAUSED: 'paused',
  GAME_OVER: 'game_over',
  MENU: 'menu'
};

// Piece types
export const PIECE_TYPES = {
  I: 'I',
  O: 'O',
  T: 'T',
  S: 'S',
  Z: 'Z',
  J: 'J',
  L: 'L'
};

// Colors for each piece type
export const PIECE_COLORS = {
  [PIECE_TYPES.I]: '#00f5ff',
  [PIECE_TYPES.O]: '#ffff00',
  [PIECE_TYPES.T]: '#a000ff',
  [PIECE_TYPES.S]: '#00ff00',
  [PIECE_TYPES.Z]: '#ff0000',
  [PIECE_TYPES.J]: '#0000ff',
  [PIECE_TYPES.L]: '#ff8000'
};
