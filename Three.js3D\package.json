{"name": "threejs-3dmodel-edit", "version": "0.1.0", "author": {"name": "answer"}, "private": true, "scripts": {"dev": "vite", "serve": "vite", "build": "vite build --mode development", "build:pro": "vite build --mode production", "test:e2e": "vue-cli-service test:e2e", "lint:lint-staged": "lint-staged", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@imengyu/vue3-context-menu": "^1.3.3", "@tweenjs/tween.js": "^18.5.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.0.1", "axios": "^1.5.0", "clipboard": "^2.0.11", "core-js": "^3.8.3", "default-passive-events": "^2.0.0", "element-plus": "^2.6.1", "mitt": "^3.0.0", "pinia": "^2.1.7", "three": "^0.176.0", "vite": "^5.2.0", "vue": "^3.5.13", "vue-router": "^4.0.13", "vue3-draggable-resizable": "^1.6.5", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-proposal-optional-chaining-assign": "^7.25.9", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@typescript-eslint/eslint-plugin": "^7.12.0", "@typescript-eslint/parser": "^7.12.0", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vue/babel-plugin-jsx": "^1.1.5", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-e2e-cypress": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "cypress": "^9.7.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^8.7.1", "husky": "^8.0.0", "lint-staged": "^15.4.3", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "prettier": "^3.3.1", "sass": "^1.72.0", "sass-loader": "^14.1.1", "stylelint": "^16.6.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.1.0", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "vite-plugin-html": "^3.2.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}