import React from 'react';

const TouchControls = ({ 
  gameState, 
  onMove, 
  onRotate, 
  onDrop, 
  onHardDrop, 
  onPause 
}) => {
  if (gameState === 'game_over') return null;

  const handleTouchStart = (e) => {
    e.preventDefault(); // Prevent scrolling
  };

  return (
    <div className="touch-controls">
      <button 
        className="touch-btn"
        onTouchStart={handleTouchStart}
        onClick={() => onMove(-1, 0)}
        disabled={gameState !== 'playing'}
      >
        ←
      </button>
      
      <button 
        className="touch-btn"
        onTouchStart={handleTouchStart}
        onClick={onRotate}
        disabled={gameState !== 'playing'}
      >
        ↻
      </button>
      
      <button 
        className="touch-btn"
        onTouchStart={handleTouchStart}
        onClick={() => onMove(1, 0)}
        disabled={gameState !== 'playing'}
      >
        →
      </button>
      
      <button 
        className="touch-btn"
        onTouchStart={handleTouchStart}
        onClick={onDrop}
        disabled={gameState !== 'playing'}
      >
        ↓
      </button>
      
      <button 
        className="touch-btn"
        onTouchStart={handleTouchStart}
        onClick={onHardDrop}
        disabled={gameState !== 'playing'}
      >
        ⬇
      </button>
      
      <button 
        className="touch-btn"
        onTouchStart={handleTouchStart}
        onClick={onPause}
        disabled={gameState === 'game_over'}
      >
        {gameState === 'paused' ? '▶' : '⏸'}
      </button>
    </div>
  );
};

export default TouchControls;
