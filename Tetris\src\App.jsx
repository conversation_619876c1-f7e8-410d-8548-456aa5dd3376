import React from 'react';
import { useGame } from './hooks/useGame.js';
import { useKeyboard } from './hooks/useKeyboard.js';
import GameBoard from './components/GameBoard.jsx';
import NextPiece from './components/NextPiece.jsx';
import GameInfo from './components/GameInfo.jsx';
import GameControls from './components/GameControls.jsx';
import Instructions from './components/Instructions.jsx';
import GameOver from './components/GameOver.jsx';
import TouchControls from './components/TouchControls.jsx';

function App() {
  const {
    board,
    currentPiece,
    nextPiece,
    score,
    lines,
    level,
    gameState,
    movePiece,
    rotatePiece,
    dropPiece,
    hardDropPiece,
    togglePause,
    restartGame,
    toggleSound,
    isSoundEnabled,
    highScore
  } = useGame();

  // Set up keyboard controls
  useKeyboard(
    gameState,
    movePiece,
    rotatePiece,
    dropPiece,
    hardDropPiece,
    togglePause,
    restartGame
  );

  return (
    <div className="app">
      <div className="game-container">
        <h1 style={{
          textAlign: 'center',
          marginBottom: '20px',
          color: '#2c3e50',
          fontSize: '2.5rem',
          fontWeight: 'bold'
        }}>
          React Tetris
        </h1>

        {gameState === 'paused' && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '20px',
            borderRadius: '10px',
            fontSize: '24px',
            fontWeight: 'bold',
            zIndex: 100
          }}>
            PAUSED
          </div>
        )}

        <GameBoard
          board={board}
          currentPiece={currentPiece}
          gameState={gameState}
        />

        <TouchControls
          gameState={gameState}
          onMove={movePiece}
          onRotate={rotatePiece}
          onDrop={dropPiece}
          onHardDrop={hardDropPiece}
          onPause={togglePause}
        />
      </div>

      <div className="sidebar">
        <NextPiece piece={nextPiece} />
        <GameInfo
          score={score}
          lines={lines}
          level={level}
          highScore={highScore}
        />
        <GameControls
          gameState={gameState}
          onPause={togglePause}
          onRestart={restartGame}
          onToggleSound={toggleSound}
          isSoundEnabled={isSoundEnabled()}
        />
        <Instructions />
      </div>

      {gameState === 'game_over' && (
        <GameOver
          score={score}
          lines={lines}
          level={level}
          onRestart={restartGame}
        />
      )}
    </div>
  );
}

export default App;
