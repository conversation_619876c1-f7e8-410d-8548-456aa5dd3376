import { useState, useEffect, useCallback, useRef } from 'react';
import {
  createBoard,
  isValidPosition,
  placePiece,
  getCompletedLines,
  clearLines,
  calculateScore,
  calculateLevel,
  calculateSpeed,
  isGameOver,
  hardDrop
} from '../utils/gameLogic.js';
import { createPiece, rotatePiece } from '../utils/pieces.js';
import { INITIAL_SPEED, SPEED_INCREASE, MIN_SPEED, SCORES } from '../utils/constants.js';
import { useSound } from './useSound.js';
import { useHighScore } from './useHighScore.js';

export const useGame = () => {
  const [board, setBoard] = useState(() => createBoard());
  const [currentPiece, setCurrentPiece] = useState(() => createPiece());
  const [nextPiece, setNextPiece] = useState(() => createPiece());
  const [score, setScore] = useState(0);
  const [lines, setLines] = useState(0);
  const [level, setLevel] = useState(0);
  const [gameState, setGameState] = useState('playing'); // 'playing', 'paused', 'game_over'

  const dropTimeRef = useRef(null);
  const gameLoopRef = useRef(null);
  const previousLevelRef = useRef(0);

  // Sound effects
  const {
    playMove,
    playRotate,
    playDrop,
    playLineClear,
    playGameOver,
    playLevelUp,
    toggleSound,
    isSoundEnabled
  } = useSound();

  // High score management
  const { highScore, updateHighScore } = useHighScore();

  // Calculate current drop speed
  const dropSpeed = calculateSpeed(level, INITIAL_SPEED, SPEED_INCREASE, MIN_SPEED);

  // Move piece
  const movePiece = useCallback((dx, dy) => {
    if (gameState !== 'playing') return false;

    const newPiece = {
      ...currentPiece,
      x: currentPiece.x + dx,
      y: currentPiece.y + dy
    };

    if (isValidPosition(board, newPiece, newPiece.x, newPiece.y)) {
      setCurrentPiece(newPiece);

      // Play sound effects
      if (dx !== 0) {
        playMove();
      }

      // Add soft drop score
      if (dy > 0) {
        setScore(prev => prev + SCORES.SOFT_DROP);
      }

      return true;
    }
    return false;
  }, [board, currentPiece, gameState]);

  // Rotate piece
  const rotatePieceAction = useCallback(() => {
    if (gameState !== 'playing') return;

    const rotatedPiece = rotatePiece(currentPiece);

    if (isValidPosition(board, rotatedPiece, rotatedPiece.x, rotatedPiece.y)) {
      setCurrentPiece(rotatedPiece);
      playRotate();
    }
  }, [board, currentPiece, gameState]);

  // Drop piece one row
  const dropPiece = useCallback(() => {
    if (gameState !== 'playing') return;

    if (!movePiece(0, 1)) {
      // Piece can't move down, place it on the board
      const newBoard = placePiece(board, currentPiece);
      const completedLines = getCompletedLines(newBoard);

      if (completedLines.length > 0) {
        const clearedBoard = clearLines(newBoard, completedLines);
        const lineScore = calculateScore(completedLines.length, level);
        const newLines = lines + completedLines.length;
        const newLevel = calculateLevel(newLines);

        setBoard(clearedBoard);
        setScore(prev => prev + lineScore);
        setLines(newLines);
        setLevel(newLevel);

        // Play line clear sound
        playLineClear(completedLines.length);

        // Play level up sound if level increased
        if (newLevel > previousLevelRef.current) {
          setTimeout(() => playLevelUp(), 500);
          previousLevelRef.current = newLevel;
        }
      } else {
        setBoard(newBoard);
        playDrop();
      }

      // Check game over
      if (isGameOver(newBoard)) {
        setGameState('game_over');
        updateHighScore(score, lines, level);
        playGameOver();
        return;
      }

      // Spawn new piece
      setCurrentPiece(nextPiece);
      setNextPiece(createPiece());
    }
  }, [board, currentPiece, nextPiece, level, lines, gameState, movePiece]);

  // Hard drop piece
  const hardDropPiece = useCallback(() => {
    if (gameState !== 'playing') return;

    const result = hardDrop(board, currentPiece);
    setCurrentPiece(result.piece);
    setScore(prev => prev + result.score);

    // Immediately place the piece
    setTimeout(() => dropPiece(), 50);
  }, [board, currentPiece, gameState, dropPiece]);

  // Pause/unpause game
  const togglePause = useCallback(() => {
    if (gameState === 'playing') {
      setGameState('paused');
    } else if (gameState === 'paused') {
      setGameState('playing');
    }
  }, [gameState]);

  // Restart game
  const restartGame = useCallback(() => {
    setBoard(createBoard());
    setCurrentPiece(createPiece());
    setNextPiece(createPiece());
    setScore(0);
    setLines(0);
    setLevel(0);
    setGameState('playing');
    dropTimeRef.current = Date.now();
  }, []);

  // Game loop
  useEffect(() => {
    if (gameState !== 'playing') return;

    const gameLoop = () => {
      const now = Date.now();

      if (!dropTimeRef.current) {
        dropTimeRef.current = now;
      }

      if (now - dropTimeRef.current >= dropSpeed) {
        dropPiece();
        dropTimeRef.current = now;
      }

      gameLoopRef.current = requestAnimationFrame(gameLoop);
    };

    dropTimeRef.current = Date.now();
    gameLoopRef.current = requestAnimationFrame(gameLoop);

    return () => {
      if (gameLoopRef.current) {
        cancelAnimationFrame(gameLoopRef.current);
      }
    };
  }, [gameState, dropSpeed, dropPiece]);

  // Reset drop timer when game state changes
  useEffect(() => {
    if (gameState === 'playing') {
      dropTimeRef.current = Date.now();
    }
  }, [gameState]);

  return {
    board,
    currentPiece,
    nextPiece,
    score,
    lines,
    level,
    gameState,
    movePiece,
    rotatePiece: rotatePieceAction,
    dropPiece,
    hardDropPiece,
    togglePause,
    restartGame,
    toggleSound,
    isSoundEnabled,
    highScore
  };
};
