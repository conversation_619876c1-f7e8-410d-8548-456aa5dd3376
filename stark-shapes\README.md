# <PERSON> Shapes

An interactive 3D particle visualization controlled through hand gestures.

Live demo: [https://collidingscopes.github.io/stark-shapes/](https://collidingscopes.github.io/stark-shapes/)

<img src="assets/siteOGImage.png">

## Features

- Control the camera with your hands
- Right hand: pinch to zoom in/out
- Left hand: rotate to orbit the camera
- Clap your hands to change to the next pattern
- 3D geometric patterns include: Cube, Sphere, Spiral, Helix, Galaxy, and more

## Technology

Built with Three.js and MediaPipe Hand Tracking.

## Creator

[@stereo.drift](https://www.instagram.com/stereo.drift/)