import React from 'react';

const NextPiece = ({ piece }) => {
  if (!piece) return null;

  const renderPreview = () => {
    const cells = [];
    
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const isActive = piece.shape[row] && piece.shape[row][col];
        const cellClass = isActive 
          ? `next-cell filled piece-${piece.type}` 
          : 'next-cell';
        
        cells.push(
          <div
            key={`${row}-${col}`}
            className={cellClass}
          />
        );
      }
    }
    
    return cells;
  };

  return (
    <div className="info-panel">
      <h3>Next Piece</h3>
      <div className="next-piece">
        {renderPreview()}
      </div>
    </div>
  );
};

export default NextPiece;
