<!doctype html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="cache-control" content="no-transform">
    <!-- <meta name="viewport" content="width=device-width,user-scalable=yes,initial-scale=0.3,minimum-scale=0.3"> -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <!-- 移动端 -->
    <!-- <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1" /> -->
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <link rel="shortcut icon" type="image/x-icon" href="./public/favicon.ico">
    <title>3D模型可视化编辑器</title>
    <meta name="description" content="基于Three.js开发的3d模型可视化编辑器，包括模型材质，场景背景，灯光，辅助线，模型位置，模型辉光，模型材质边缘发光，模型拆分，外部模型上传等功能">
    <meta name="keyword"
        content="three.js,three,3d模型编辑器,模型编辑,three.js材质,材质,three.js背景图,three.js全景图,背景图,全景图,发光,辉光,模型拆分,辅助线,灯光,平行光,点光源,聚光灯,环境光,灯光辅助线,three.js材质贴图,,模型拖拽, 材质高亮,局部辉光,three.js模型加载">
    <style>
        #loading-mask {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 9999;
            width: 100%;
            height: 100%;
            overflow: hidden;
            user-select: none;
            background: #ffffff
        }

        .loading-wrapper {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -100%)
        }

        .loading-dot {
            position: relative;
            box-sizing: border-box;
            display: inline-block;
            width: 64px;
            height: 64px;
            font-size: 64px;
            transform: rotate(45deg);
            animation: antRotate 1.2s infinite linear
        }

        .loading-dot i {
            position: absolute;
            display: block;
            width: 22px;
            height: 22px;
            background-color: #1890ff;
            border-radius: 100%;
            opacity: .3;
            transform: scale(.75);
            transform-origin: 50% 50%;
            animation: antSpinMove 1s infinite linear alternate
        }

        .loading-dot i:first-child {
            top: 0;
            left: 0
        }

        .loading-dot i:nth-child(2) {
            top: 0;
            right: 0;
            animation-delay: .4s;
            animation-delay: .4s
        }

        .loading-dot i:nth-child(3) {
            right: 0;
            bottom: 0;
            animation-delay: .8s;
            animation-delay: .8s
        }

        .loading-dot i:nth-child(4) {
            bottom: 0;
            left: 0;
            animation-delay: 1.2s;
            animation-delay: 1.2s
        }

        @keyframes antRotate {
            to {
                transform: rotate(405deg);
                transform: rotate(405deg)
            }
        }

        @keyframes antRotate {
            to {
                transform: rotate(405deg);
                transform: rotate(405deg)
            }
        }

        @keyframes antSpinMove {
            to {
                opacity: 1
            }
        }

        @keyframes antSpinMove {
            to {
                opacity: 1
            }
        }
    </style>
    <script>var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?606a102b298cb00317d5a96037729e23";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();</script>
</head><noscript></noscript>
<script type="module" src="/src/main.js"></script>
<div id="app">
    <div id="loading-mask">
        <div class="loading-wrapper">
            <span class="loading-dot loading-dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
    </div>

</html>