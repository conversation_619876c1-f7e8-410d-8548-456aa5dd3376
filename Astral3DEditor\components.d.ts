/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AssetsLibrary: typeof import('./src/components/assets/AssetsLibrary.vue')['default']
    BIMLibrary: typeof import('./src/components/assets/BIMLibrary.vue')['default']
    BIMProperties: typeof import('./src/components/viewport/BIMProperties.vue')['default']
    BimUploadDialog: typeof import('./src/components/assets/bimLibrary/BimUploadDialog.vue')['default']
    CAD: typeof import('./src/components/drawing/toolbar/CAD.vue')['default']
    CadLayers: typeof import('./src/components/drawing/toolbar/CadLayers.vue')['default']
    CadLibrary: typeof import('./src/components/assets/CadLibrary.vue')['default']
    CadUploadDialog: typeof import('./src/components/assets/cadLibrary/CadUploadDialog.vue')['default']
    Cameras: typeof import('./src/components/assets/assetsLibrary/Cameras.vue')['default']
    CesiumConfig: typeof import('./src/components/project/CesiumConfig.vue')['default']
    Clear: typeof import('./src/components/header/navigation/Clear.vue')['default']
    Color: typeof import('./src/components/setting/common/Color.vue')['default']
    CommonSetting: typeof import('./src/components/setting/CommonSetting.vue')['default']
    Copy: typeof import('./src/components/header/navigation/Copy.vue')['default']
    Delete: typeof import('./src/components/header/navigation/Delete.vue')['default']
    Do: typeof import('./src/components/header/navigation/Do.vue')['default']
    Drawing: typeof import('./src/components/drawing/Drawing.vue')['default']
    EsContextmenu: typeof import('./src/components/es/EsContextmenu.vue')['default']
    EsCubeLoading: typeof import('./src/components/es/EsCubeLoading.vue')['default']
    EsDocument: typeof import('./src/components/es/EsDocument.vue')['default']
    EsInput: typeof import('./src/components/es/EsInput.vue')['default']
    EsInputNumber: typeof import('./src/components/es/EsInputNumber.vue')['default']
    EsTexture: typeof import('./src/components/es/EsTexture.vue')['default']
    EsTip: typeof import('./src/components/es/EsTip.vue')['default']
    Fullscreen: typeof import('./src/components/header/navigation/Fullscreen.vue')['default']
    HtmlPlane: typeof import('./src/components/assets/assetsLibrary/HtmlPlane.vue')['default']
    IFCProperties: typeof import('./src/components/viewport/IFCProperties.vue')['default']
    Image: typeof import('./src/components/drawing/toolbar/Image.vue')['default']
    ImportExport: typeof import('./src/components/header/navigation/ImportExport.vue')['default']
    Lights: typeof import('./src/components/assets/assetsLibrary/Lights.vue')['default']
    Locale: typeof import('./src/components/setting/common/Locale.vue')['default']
    Logo: typeof import('./src/components/header/Logo.vue')['default']
    Material: typeof import('./src/components/assets/assetsLibrary/Material.vue')['default']
    MaterialPreview: typeof import('./src/components/three/MaterialPreview.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NavigationOperation: typeof import('./src/components/header/NavigationOperation.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCarousel: typeof import('naive-ui')['NCarousel']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGradientText: typeof import('naive-ui')['NGradientText']
    NIcon: typeof import('naive-ui')['NIcon']
    NImage: typeof import('naive-ui')['NImage']
    NInfiniteScroll: typeof import('naive-ui')['NInfiniteScroll']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutFooter: typeof import('naive-ui')['NLayoutFooter']
    NLayoutHeader: typeof import('naive-ui')['NLayoutHeader']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NModalProvider: typeof import('naive-ui')['NModalProvider']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopover: typeof import('naive-ui')['NPopover']
    NPopselect: typeof import('naive-ui')['NPopselect']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NResult: typeof import('naive-ui')['NResult']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSlider: typeof import('naive-ui')['NSlider']
    NSpin: typeof import('naive-ui')['NSpin']
    NSplit: typeof import('naive-ui')['NSplit']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    Object3d: typeof import('./src/components/assets/assetsLibrary/Object3d.vue')['default']
    ProjectInfoForm: typeof import('./src/components/project/ProjectInfoForm.vue')['default']
    RightOperation: typeof import('./src/components/header/RightOperation.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SaveToService: typeof import('./src/components/header/right/SaveToService.vue')['default']
    SceneAllMaterials: typeof import('./src/components/sidebar/material/SceneAllMaterials.vue')['default']
    SceneTree: typeof import('./src/components/tree/SceneTree.vue')['default']
    Script: typeof import('./src/components/code/Script.vue')['default']
    Setting: typeof import('./src/components/setting/Setting.vue')['default']
    SettingTabs: typeof import('./src/components/setting/components/SettingTabs.vue')['default']
    Shortcuts: typeof import('./src/components/setting/components/Shortcuts.vue')['default']
    'Sidebar.Effect.Bokeh': typeof import('./src/components/sidebar/effect/Sidebar.Effect.Bokeh.vue')['default']
    'Sidebar.Effect.FXAA': typeof import('./src/components/sidebar/effect/Sidebar.Effect.FXAA.vue')['default']
    'Sidebar.Effect.Halftone': typeof import('./src/components/sidebar/effect/Sidebar.Effect.Halftone.vue')['default']
    'Sidebar.Effect.Outline': typeof import('./src/components/sidebar/effect/Sidebar.Effect.Outline.vue')['default']
    'Sidebar.Effect.Pixelate': typeof import('./src/components/sidebar/effect/Sidebar.Effect.Pixelate.vue')['default']
    'Sidebar.Effect.UnrealBloom': typeof import('./src/components/sidebar/effect/Sidebar.Effect.UnrealBloom.vue')['default']
    'Sidebar.Geometry.BoxGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.BoxGeometry.vue')['default']
    'Sidebar.Geometry.BufferGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.BufferGeometry.vue')['default']
    'Sidebar.Geometry.CapsuleGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.CapsuleGeometry.vue')['default']
    'Sidebar.Geometry.CircleGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.CircleGeometry.vue')['default']
    'Sidebar.Geometry.CylinderGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.CylinderGeometry.vue')['default']
    'Sidebar.Geometry.DodecahedronGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.DodecahedronGeometry.vue')['default']
    'Sidebar.Geometry.ExtrudeGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.ExtrudeGeometry.vue')['default']
    'Sidebar.Geometry.IcosahedronGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.IcosahedronGeometry.vue')['default']
    'Sidebar.Geometry.LatheGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.LatheGeometry.vue')['default']
    'Sidebar.Geometry.Modifiers': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.Modifiers.vue')['default']
    'Sidebar.Geometry.OctahedronGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.OctahedronGeometry.vue')['default']
    'Sidebar.Geometry.PlaneGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.PlaneGeometry.vue')['default']
    'Sidebar.Geometry.RingGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.RingGeometry.vue')['default']
    'Sidebar.Geometry.ShapeGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.ShapeGeometry.vue')['default']
    'Sidebar.Geometry.SphereGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.SphereGeometry.vue')['default']
    'Sidebar.Geometry.TeapotGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.TeapotGeometry.vue')['default']
    'Sidebar.Geometry.TetrahedronGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.TetrahedronGeometry.vue')['default']
    'Sidebar.Geometry.TorusGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.TorusGeometry.vue')['default']
    'Sidebar.Geometry.TorusKnotGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.TorusKnotGeometry.vue')['default']
    'Sidebar.Geometry.TubeGeometry': typeof import('./src/components/sidebar/geometry/Sidebar.Geometry.TubeGeometry.vue')['default']
    'Sidebar.Scene.ProjectSetting': typeof import('./src/components/sidebar/scene/Sidebar.Scene.ProjectSetting.vue')['default']
    'Sidebar.Scene.Render': typeof import('./src/components/sidebar/scene/Sidebar.Scene.Render.vue')['default']
    'Sidebar.Scene.Setting': typeof import('./src/components/sidebar/scene/Sidebar.Scene.Setting.vue')['default']
    SidebarAnimations: typeof import('./src/components/sidebar/SidebarAnimations.vue')['default']
    SidebarDrawing: typeof import('./src/components/sidebar/SidebarDrawing.vue')['default']
    SidebarEffect: typeof import('./src/components/sidebar/SidebarEffect.vue')['default']
    SidebarGeometry: typeof import('./src/components/sidebar/SidebarGeometry.vue')['default']
    SidebarHistory: typeof import('./src/components/sidebar/SidebarHistory.vue')['default']
    SidebarMaterial: typeof import('./src/components/sidebar/SidebarMaterial.vue')['default']
    SidebarMaterialBooleanProperty: typeof import('./src/components/sidebar/material/SidebarMaterialBooleanProperty.vue')['default']
    SidebarMaterialColorProperty: typeof import('./src/components/sidebar/material/SidebarMaterialColorProperty.vue')['default']
    SidebarMaterialConstantProperty: typeof import('./src/components/sidebar/material/SidebarMaterialConstantProperty.vue')['default']
    SidebarMaterialMapProperty: typeof import('./src/components/sidebar/material/SidebarMaterialMapProperty.vue')['default']
    SidebarMaterialNumberProperty: typeof import('./src/components/sidebar/material/SidebarMaterialNumberProperty.vue')['default']
    SidebarMaterialProgram: typeof import('./src/components/sidebar/material/SidebarMaterialProgram.vue')['default']
    SidebarMaterialRangeValueProperty: typeof import('./src/components/sidebar/material/SidebarMaterialRangeValueProperty.vue')['default']
    SidebarObject: typeof import('./src/components/sidebar/SidebarObject.vue')['default']
    SidebarScene: typeof import('./src/components/sidebar/SidebarScene.vue')['default']
    SidebarSceneTheme: typeof import('./src/components/sidebar/SidebarSceneTheme.vue')['default']
    SidebarScript: typeof import('./src/components/sidebar/SidebarScript.vue')['default']
    SystemSetting: typeof import('./src/components/setting/components/SystemSetting.vue')['default']
    Theme: typeof import('./src/components/setting/common/Theme.vue')['default']
    Toolbar: typeof import('./src/components/viewport/Toolbar.vue')['default']
    UserData: typeof import('./src/components/code/UserData.vue')['default']
    Viewport: typeof import('./src/components/viewport/Viewport.vue')['default']
    ViewportCamera: typeof import('./src/components/viewport/ViewportCamera.vue')['default']
    ViewportInfo: typeof import('./src/components/viewport/ViewportInfo.vue')['default']
    ViewportShading: typeof import('./src/components/viewport/ViewportShading.vue')['default']
    XR: typeof import('./src/components/header/right/XR.vue')['default']
  }
}
