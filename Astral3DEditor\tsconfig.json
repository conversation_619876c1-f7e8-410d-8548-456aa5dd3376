{
  "compilerOptions": {
    "useDefineForClassFields": true,
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "strictFunctionTypes": false,
    "jsx": "preserve",
    "baseUrl": ".",
    "allowJs": true,
    "sourceMap": false,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "experimentalDecorators": true,
    "lib": ["dom", "esnext"],
    "types": ["vite/client","vite-plugin-pwa/client"],
    "typeRoots": ["./node_modules/@types/", "./types","./public/sdk/types"],
    "noImplicitAny": false,
    "skipLibCheck": true,
    "paths": {
      "~/*": [
        "types/*"
      ],
      "@/*": [
        "src/*"
      ]
    },
    "emitDeclarationOnly": true, // 只输出声明文件(ts 产物)
    "declaration": true, // 自动生成声明文件
    "declarationDir": "types", // 声明文件生成的目录
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/*.d.ts",
    "types/**/*.d.ts",
    "build/**/*.ts",
    "public/sdk/**/*.d.ts"
  ],
  "exclude": ["node_modules",  "dist"]
}