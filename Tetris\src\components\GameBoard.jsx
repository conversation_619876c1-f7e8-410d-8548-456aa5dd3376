import React from 'react';
import { BOARD_WIDTH, BOARD_HEIGHT } from '../utils/constants.js';

const GameBoard = ({ board, currentPiece, gameState }) => {
  // Create a display board that includes the current falling piece
  const createDisplayBoard = () => {
    // Start with the static board
    const displayBoard = board.map(row => row.map(cell => ({ ...cell })));
    
    // Add the current piece if it exists and game is playing
    if (currentPiece && (gameState === 'playing' || gameState === 'paused')) {
      for (let row = 0; row < currentPiece.shape.length; row++) {
        for (let col = 0; col < currentPiece.shape[row].length; col++) {
          if (currentPiece.shape[row][col]) {
            const x = currentPiece.x + col;
            const y = currentPiece.y + row;
            
            if (y >= 0 && y < BOARD_HEIGHT && x >= 0 && x < BOARD_WIDTH) {
              displayBoard[y][x] = {
                type: currentPiece.type,
                filled: true,
                isCurrentPiece: true
              };
            }
          }
        }
      }
    }
    
    return displayBoard;
  };

  const displayBoard = createDisplayBoard();

  const getCellClass = (cell) => {
    if (!cell.filled) return 'cell empty';
    
    const baseClass = 'cell filled';
    const pieceClass = `piece-${cell.type}`;
    const currentClass = cell.isCurrentPiece ? 'current-piece' : '';
    
    return `${baseClass} ${pieceClass} ${currentClass}`.trim();
  };

  return (
    <div className="game-board">
      {displayBoard.map((row, rowIndex) =>
        row.map((cell, colIndex) => (
          <div
            key={`${rowIndex}-${colIndex}`}
            className={getCellClass(cell)}
          />
        ))
      )}
    </div>
  );
};

export default GameBoard;
